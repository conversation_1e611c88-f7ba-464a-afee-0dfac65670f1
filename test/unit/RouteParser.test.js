const fs = require('fs-extra');
const path = require('path');
const RouteParser = require('../../src/domain/runtime-validation/router/VueRouteParser');

describe('RouteParser', () => {
  let routeParser;
  const testProjectPath = '/Users/<USER>/works/galaxy/galaxy-vue3-demi';

  beforeEach(() => {
    routeParser = new RouteParser(testProjectPath, {
      verbose: true,
      useAI: false // 禁用AI，只测试静态解析
    });
  });

  describe('parseRoutes', () => {
    it('should parse all routes from galaxy-vue3-demi project', async () => {
      const result = await routeParser.parseRoutes();

      console.log('解析结果:', result);
      console.log('找到的路由数量:', result.routes.length);
      console.log('路由详情:', JSON.stringify(result.routes, null, 2));

      // 验证解析成功
      expect(result.success).toBe(true);

      // 验证路由数量 - 应该解析出更多路由
      expect(result.routes.length).toBeGreaterThan(6);

      // 验证包含预期的路由
      const routePaths = routeParser.getAllRoutePaths();
      console.log('所有路由路径:', routePaths.map(r => r.path));

      // 检查是否包含预期的路由
      const expectedPaths = [
        '/dashboard',
        '/login',
        '/normal-login',
        '/sso-login',
        '/404',
        '/401',
        '/change-password'
      ];

      const actualPaths = routePaths.map(r => r.path);
      for (const expectedPath of expectedPaths) {
        expect(actualPaths).toContain(expectedPath);
      }
    });

    it('should correctly parse constantRoutes array', async () => {
      // 直接测试路由文件内容解析
      const routerFilePath = path.join(testProjectPath, 'src/router/index.js');
      const content = await fs.readFile(routerFilePath, 'utf8');

      const routes = await routeParser.staticParseRoutes(content, 'src/router/index.js');

      console.log('静态解析的路由:', JSON.stringify(routes, null, 2));
      console.log('静态解析的路由数量:', routes.length);

      // 验证能解析出路由
      expect(routes.length).toBeGreaterThan(0);

      // 查找所有路由路径
      const allPaths = [];
      const extractPaths = (routeList, basePath = '') => {
        for (const route of routeList) {
          if (route.path) {
            let fullPath = route.path;
            if (!route.path.startsWith('/') && basePath) {
              fullPath = basePath + '/' + route.path;
            }
            allPaths.push(fullPath);
            console.log('找到路由路径:', fullPath, '名称:', route.name);
          }
          if (route.children && route.children.length > 0) {
            extractPaths(route.children, route.path || basePath);
          }
        }
      };

      extractPaths(routes);
      console.log('所有提取的路径:', allPaths);

      // 验证包含dashboard路由
      expect(allPaths.some(path => path.includes('dashboard'))).toBe(true);
    });

    it('should handle nested routes correctly', async () => {
      const routerFilePath = path.join(testProjectPath, 'src/router/index.js');
      const content = await fs.readFile(routerFilePath, 'utf8');

      const routes = await routeParser.staticParseRoutes(content, 'src/router/index.js');

      // 查找有子路由的路由
      const routesWithChildren = routes.filter(r => r.children && r.children.length > 0);
      console.log('有子路由的路由:', routesWithChildren);

      expect(routesWithChildren.length).toBeGreaterThan(0);

      // 验证子路由解析 - 需要先设置routes
      routeParser.routes = routes;
      const allPaths = routeParser.getAllRoutePaths();
      console.log('展开后的所有路径:', allPaths.map(r => r.path));

      // 应该包含展开的子路由路径
      expect(allPaths.some(r => r.path === '/dashboard')).toBe(true);
    });
  });

  describe('extractRoutesFromArray', () => {
    it('should extract routes from constantRoutes structure', () => {
      // 模拟constantRoutes的AST结构
      const mockArrayNode = {
        type: 'ArrayExpression',
        elements: [
          {
            type: 'ObjectExpression',
            properties: [
              {
                key: { type: 'Identifier', name: 'path' },
                value: { type: 'StringLiteral', value: '/login' }
              },
              {
                key: { type: 'Identifier', name: 'hidden' },
                value: { type: 'BooleanLiteral', value: true }
              }
            ]
          }
        ]
      };

      const routes = routeParser.extractRoutesFromArray(mockArrayNode);
      expect(routes.length).toBe(1);
      expect(routes[0].path).toBe('/login');
      expect(routes[0].hidden).toBe(true);
    });
  });
});
