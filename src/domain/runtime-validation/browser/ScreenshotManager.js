const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * ScreenshotManager - 截图管理器
 *
 * 职责：
 * 1. 管理页面截图功能
 * 2. 处理截图文件存储
 * 3. 生成截图文件名和路径
 * 4. 处理截图错误和异常情况
 */
class ScreenshotManager {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      screenshotDir: 'validation-reports/screenshots',
      viewport: { width: 1280, height: 800 },
      fullPage: true,
      type: 'png',
      quality: 90,
      verbose: false,
      ...options
    };

    this.screenshotDir = path.join(this.projectPath, this.options.screenshotDir);
  }

  /**
   * 初始化截图目录
   */
  async initialize() {
    try {
      await fs.ensureDir(this.screenshotDir);
      if (this.options.verbose) {
        console.log(chalk.gray(`   📁 截图目录已准备: ${this.screenshotDir}`));
      }
    } catch (error) {
      throw new Error(`初始化截图目录失败: ${error.message}`);
    }
  }

  /**
   * 为页面截图
   */
  async takeScreenshot(page, route, options = {}) {
    try {
      // 检查当前页面URL是否有效
      const currentUrl = page.url();
      if (!this.isValidUrl(currentUrl)) {
        throw new Error(`无法对错误页面截图: ${currentUrl}`);
      }

      // 确保截图目录存在
      await this.initialize();

      // 生成截图文件路径
      const screenshotPath = this.generateScreenshotPath(route, options);

      // 设置视口大小
      await this.setViewport(page, options.viewport);

      // 等待页面稳定
      await this.waitForPageStable(page, options.waitTime);

      // 执行截图
      const screenshotOptions = this.buildScreenshotOptions(screenshotPath, options);
      await page.screenshot(screenshotOptions);

      if (this.options.verbose) {
        console.log(chalk.gray(`    📸 页面截图已保存: ${screenshotPath}`));
      }

      return screenshotPath;

    } catch (error) {
      const currentUrl = page.url();
      const enhancedError = new Error(`截图失败 (URL: ${currentUrl}): ${error.message}`);
      enhancedError.originalError = error;
      throw enhancedError;
    }
  }

  /**
   * 检查URL是否有效
   */
  isValidUrl(url) {
    return url &&
           url !== 'about:blank' &&
           !url.startsWith('chrome-error://') &&
           !url.startsWith('edge-error://') &&
           !url.startsWith('moz-error://');
  }

  /**
   * 生成截图文件路径
   */
  generateScreenshotPath(route, options = {}) {
    const timestamp = options.timestamp || new Date().toISOString().replace(/[:.]/g, '-');
    const routeName = this.sanitizeRouteName(route.path);
    const suffix = options.suffix || '';
    const extension = options.type || this.options.type;

    const filename = `${routeName}${suffix}_${timestamp}.${extension}`;
    return path.join(this.screenshotDir, filename);
  }

  /**
   * 清理路由名称用作文件名
   */
  sanitizeRouteName(routePath) {
    return routePath
      .replace(/[\/\?#]/g, '_')
      .replace(/^_/, '')
      .replace(/_+/g, '_')
      .replace(/[<>:"|*]/g, '') // 移除Windows不支持的字符
      || 'root';
  }

  /**
   * 设置视口大小
   */
  async setViewport(page, customViewport) {
    try {
      const viewport = customViewport || this.options.viewport;
      await page.setViewport(viewport);
    } catch (viewportError) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`    ⚠️  设置视口失败: ${viewportError.message}`));
      }
      // 继续尝试截图，即使视口设置失败
    }
  }

  /**
   * 等待页面稳定
   */
  async waitForPageStable(page, waitTime = 1000) {
    try {
      await new Promise(resolve => setTimeout(resolve, waitTime));

      // 可选：等待网络空闲
      if (this.options.waitForNetworkIdle) {
        await page.waitForLoadState('networkidle', { timeout: 5000 });
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`    ⚠️  等待页面稳定失败: ${error.message}`));
      }
    }
  }

  /**
   * 构建截图选项
   */
  buildScreenshotOptions(screenshotPath, options = {}) {
    return {
      path: screenshotPath,
      fullPage: options.fullPage !== undefined ? options.fullPage : this.options.fullPage,
      type: options.type || this.options.type,
      quality: options.quality || this.options.quality,
      ...options.screenshotOptions
    };
  }
}

module.exports = ScreenshotManager;
