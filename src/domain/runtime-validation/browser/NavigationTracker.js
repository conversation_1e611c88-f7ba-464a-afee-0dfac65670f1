const chalk = require('chalk');

/**
 * NavigationTracker - 页面导航跟踪器
 * 
 * 职责：
 * 1. 跟踪页面导航和跳转
 * 2. 检测登录重定向和错误页面
 * 3. 记录导航历史
 * 4. 分析导航模式
 */
class NavigationTracker {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      trackRedirects: true,
      trackLoginRedirects: true,
      trackErrorPages: true,
      ...options
    };

    this.navigationHistory = [];
    this.redirectPatterns = {
      login: [
        /\/login/i,
        /\/signin/i,
        /\/auth/i,
        /\/authentication/i,
        /\/sso/i
      ],
      error: [
        /\/error/i,
        /\/404/i,
        /\/403/i,
        /\/500/i,
        /\/not-found/i,
        /\/unauthorized/i,
        /\/forbidden/i
      ]
    };
  }

  /**
   * 设置页面导航跟踪
   */
  setupNavigationTracking(page, result) {
    // 初始化跳转记录
    if (!result.navigationHistory) {
      result.navigationHistory = [];
    }

    // 记录初始URL
    const initialUrl = page.url();
    if (initialUrl && initialUrl !== 'about:blank') {
      this.addNavigationEntry(result, {
        url: initialUrl,
        type: 'initial',
        reason: 'page_load'
      });
    }

    // 监听页面跳转事件
    let lastUrl = initialUrl;

    page.on('load', () => {
      const currentUrl = page.url();
      const timestamp = new Date().toISOString();

      // 如果URL发生变化，记录跳转
      if (currentUrl !== lastUrl && currentUrl !== 'about:blank') {
        const navigationEntry = {
          url: currentUrl,
          timestamp: timestamp,
          type: 'navigation',
          reason: 'page_load'
        };

        this.analyzeNavigation(navigationEntry);
        this.addNavigationEntry(result, navigationEntry);

        // 输出跳转日志
        if (this.options.verbose) {
          console.log(chalk.cyan(`    🔄 页面跳转检测: ${currentUrl}`));
        }

        lastUrl = currentUrl;
      }
    });

    // 监听页面响应事件（用于检测HTTP重定向）
    if (this.options.trackRedirects) {
      page.on('response', (response) => {
        const status = response.status();
        const url = response.url();

        // 检测重定向状态码
        if (status >= 300 && status < 400) {
          const redirectEntry = {
            url: url,
            timestamp: new Date().toISOString(),
            type: 'redirect',
            reason: `http_${status}`,
            statusCode: status,
            statusText: response.statusText()
          };

          this.addNavigationEntry(result, redirectEntry);

          if (this.options.verbose) {
            console.log(chalk.magenta(`    ↩️  HTTP重定向检测: ${status} ${url}`));
          }
        }
      });
    }

    return this;
  }

  /**
   * 添加导航记录
   */
  addNavigationEntry(result, entry) {
    entry.timestamp = entry.timestamp || new Date().toISOString();
    result.navigationHistory.push(entry);
    this.navigationHistory.push(entry);
  }

  /**
   * 分析导航类型
   */
  analyzeNavigation(navigationEntry) {
    const url = navigationEntry.url;

    // 检查是否是登录重定向
    if (this.options.trackLoginRedirects && this.isLoginRedirect(url)) {
      navigationEntry.isLoginRedirect = true;
      if (this.options.verbose) {
        console.log(chalk.yellow(`    🔐 检测到登录页面重定向: ${url}`));
      }
    }

    // 检查是否是错误页面
    if (this.options.trackErrorPages && this.isErrorPage(url)) {
      navigationEntry.isErrorPage = true;
      if (this.options.verbose) {
        console.log(chalk.red(`    ❌ 检测到错误页面跳转: ${url}`));
      }
    }

    return navigationEntry;
  }

  /**
   * 检查是否是登录重定向
   */
  isLoginRedirect(url) {
    return this.redirectPatterns.login.some(pattern => pattern.test(url));
  }

  /**
   * 检查是否是错误页面
   */
  isErrorPage(url) {
    return this.redirectPatterns.error.some(pattern => pattern.test(url));
  }

  /**
   * 记录导航后的跳转
   */
  recordPostNavigationRedirect(originalUrl, finalUrl, result) {
    if (finalUrl !== originalUrl) {
      const navigationEntry = {
        url: finalUrl,
        timestamp: new Date().toISOString(),
        type: 'navigation',
        reason: 'post_navigation_redirect',
        originalUrl: originalUrl
      };

      this.analyzeNavigation(navigationEntry);
      this.addNavigationEntry(result, navigationEntry);

      if (this.options.verbose) {
        console.log(chalk.cyan(`    🔄 导航后检测到跳转: ${originalUrl} → ${finalUrl}`));
      }
    }
  }

  /**
   * 输出页面跳转历史日志
   */
  logNavigationHistory(result) {
    if (!result.navigationHistory || result.navigationHistory.length === 0) {
      return;
    }

    // 统计跳转信息
    const stats = this.getNavigationStats(result.navigationHistory);

    // 如果有跳转，输出概要信息
    if (stats.totalNavigations > 1) {
      console.log(chalk.blue(`    🔄 页面跳转检测: 共发生 ${stats.totalNavigations - 1} 次跳转`));

      if (stats.redirects > 0) {
        console.log(chalk.yellow(`    ↩️  HTTP重定向: ${stats.redirects} 次`));
      }

      if (stats.loginRedirects > 0) {
        console.log(chalk.cyan(`    🔐 登录页面重定向: ${stats.loginRedirects} 次`));
      }

      if (stats.errorPages > 0) {
        console.log(chalk.red(`    ❌ 错误页面跳转: ${stats.errorPages} 次`));
      }

      // 详细跳转历史（仅在 verbose 模式下显示）
      if (this.options.verbose) {
        this.logDetailedNavigationHistory(result.navigationHistory);
      }
    } else {
      // 没有跳转的情况
      if (this.options.verbose) {
        console.log(chalk.green(`    ✅ 页面无跳转，直接加载成功`));
      }
    }
  }

  /**
   * 输出详细的跳转历史
   */
  logDetailedNavigationHistory(navigationHistory) {
    console.log(chalk.gray(`    📋 跳转历史详情:`));
    
    navigationHistory.forEach((nav, index) => {
      const time = new Date(nav.timestamp).toLocaleTimeString();
      let icon = '🔗';
      let color = chalk.gray;

      if (nav.type === 'initial') {
        icon = '🏠';
        color = chalk.blue;
      } else if (nav.type === 'redirect') {
        icon = '↩️';
        color = chalk.yellow;
      } else if (nav.isLoginRedirect) {
        icon = '🔐';
        color = chalk.cyan;
      } else if (nav.isErrorPage) {
        icon = '❌';
        color = chalk.red;
      }

      const statusInfo = nav.statusCode ? ` (${nav.statusCode})` : '';
      console.log(color(`      ${index + 1}. ${icon} ${time} - ${nav.url}${statusInfo}`));
    });
  }

  /**
   * 获取导航统计信息
   */
  getNavigationStats(navigationHistory) {
    const totalNavigations = navigationHistory.length;
    const redirects = navigationHistory.filter(nav => nav.type === 'redirect').length;
    const loginRedirects = navigationHistory.filter(nav => nav.isLoginRedirect).length;
    const errorPages = navigationHistory.filter(nav => nav.isErrorPage).length;

    return {
      totalNavigations,
      redirects,
      loginRedirects,
      errorPages
    };
  }

  /**
   * 生成导航报告
   */
  generateNavigationReport(allResults) {
    let totalNavigations = 0;
    let pagesWithRedirects = 0;
    let loginRedirects = 0;
    let errorPageRedirects = 0;
    let httpRedirects = 0;

    for (const result of allResults) {
      if (result.navigationHistory && result.navigationHistory.length > 0) {
        const navigations = result.navigationHistory.length - 1; // 减去初始页面
        if (navigations > 0) {
          totalNavigations += navigations;
          pagesWithRedirects++;

          // 统计不同类型的跳转
          for (const nav of result.navigationHistory) {
            if (nav.isLoginRedirect) {
              loginRedirects++;
            }
            if (nav.isErrorPage) {
              errorPageRedirects++;
            }
            if (nav.type === 'redirect') {
              httpRedirects++;
            }
          }
        }
      }
    }

    return {
      totalNavigations,
      pagesWithRedirects,
      loginRedirects,
      errorPageRedirects,
      httpRedirects
    };
  }

  /**
   * 重置跟踪状态
   */
  reset() {
    this.navigationHistory = [];
  }

  /**
   * 添加自定义重定向模式
   */
  addRedirectPattern(type, pattern) {
    if (this.redirectPatterns[type]) {
      this.redirectPatterns[type].push(pattern);
    }
  }
}

module.exports = NavigationTracker;
