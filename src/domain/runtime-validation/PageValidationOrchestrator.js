const chalk = require('chalk');
const DevServerManager = require('./DevServerManager');
const ScreenshotManager = require('./browser/ScreenshotManager');
const NavigationTracker = require('./browser/NavigationTracker');
const BrowserDetector = require('./browser/BrowserDetector');
const AutoLoginManager = require('./login/AutoLoginManager');
const { browserFactory } = require('../../infrastructure/browser');

/**
 * PageValidationOrchestrator - 页面验证编排器
 *
 * 职责：
 * 1. 协调各个组件完成页面验证
 * 2. 管理验证流程的生命周期
 * 3. 处理组件间的依赖关系
 * 4. 提供统一的验证接口
 */
class PageValidationOrchestrator {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      port: 3000,
      timeout: 30000,
      headless: 'new',
      devCommand: 'npm run dev',
      baseUrl: null,
      verbose: false,
      pageTimeout: 15000,
      navigationTimeout: 20000,
      routerMode: 'hash',
      loginCredentials: {
        username: 'admin',
        password: '111111'
      },
      skipLogin: false,
      ...options
    };

    // 初始化各个管理器
    this.devServerManager = new DevServerManager(projectPath, {
      port: this.options.port,
      devCommand: this.options.devCommand,
      baseUrl: this.options.baseUrl,
      waitForServer: this.options.waitForServer || 60000,
      verbose: this.options.verbose
    });

    this.screenshotManager = new ScreenshotManager(projectPath, {
      verbose: this.options.verbose
    });

    this.navigationTracker = new NavigationTracker({
      verbose: this.options.verbose
    });

    this.browserDetector = new BrowserDetector({
      verbose: this.options.verbose,
      preferredBrowsers: ['chrome', 'chromium', 'edge']
    });

    this.autoLoginManager = new AutoLoginManager({
      username: this.options.loginCredentials?.username || this.options.username || 'admin',
      password: this.options.loginCredentials?.password || this.options.password || '111111',
      verbose: this.options.verbose,
      aiEnabled: !this.options.skipLogin,
      configPath: require('path').join(projectPath, '.login-config.json')
    });

    // 浏览器相关
    this.browser = null;
    this.browserAutomation = null;
    this.selectedBrowserType = null;
  }

  /**
   * 初始化所有组件
   */
  async initialize() {
    try {
      // 1. 启动开发服务器
      await this.devServerManager.startDevServer();

      // 2. 初始化截图管理器
      await this.screenshotManager.initialize();

      // 3. 启动浏览器
      await this.startBrowser();

      console.log(chalk.green('✅ 页面验证组件初始化完成'));
      return true;
    } catch (error) {
      console.error(chalk.red(`❌ 组件初始化失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 启动浏览器
   */
  async startBrowser() {
    console.log(chalk.gray(`   启动浏览器...`));

    // 确保浏览器可用
    await this.ensureBrowser();

    const launchOptions = {
      headless: this.options.headless === 'new' ? 'new' : this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    };

    // 如果检测到系统浏览器，使用它
    if (this.options.executablePath) {
      launchOptions.executablePath = this.options.executablePath;
    }

    try {
      // 使用抽象层启动浏览器
      this.browserAutomation = await this.createBrowserAutomation();
      this.browser = await this.browserAutomation.launch(launchOptions);
      console.log(chalk.green('✅ 浏览器启动成功'));
    } catch (error) {
      // 如果启动失败，尝试使用新的 headless 模式
      if (!launchOptions.headless || launchOptions.headless === true) {
        console.log(chalk.yellow('⚠️  尝试使用新的 headless 模式...'));
        launchOptions.headless = 'new';
        this.browser = await this.browserAutomation.launch(launchOptions);
        console.log(chalk.green('✅ 浏览器启动成功 (新 headless 模式)'));
      } else {
        throw error;
      }
    }
  }

  /**
   * 确保浏览器可用
   */
  async ensureBrowser() {
    try {
      const selectedBrowser = await this.browserDetector.ensureBrowser();

      // 如果选择的是系统浏览器，设置可执行文件路径
      if (selectedBrowser.type !== 'puppeteer') {
        this.options.executablePath = selectedBrowser.executablePath;
      }

      // 设置浏览器类型以便后续创建正确的适配器
      this.selectedBrowserType = selectedBrowser.type;

      return selectedBrowser;
    } catch (error) {
      throw new Error(`浏览器检测失败: ${error.message}`);
    }
  }

  /**
   * 创建浏览器自动化实例
   */
  async createBrowserAutomation() {
    try {
      // 如果已经有选择的浏览器类型，使用它
      if (this.selectedBrowserType) {
        return browserFactory.createBrowserAutomation(this.selectedBrowserType);
      }

      // 否则自动选择最佳的浏览器
      const preferredTypes = ['puppeteer', 'playwright-chromium', 'playwright-firefox', 'playwright-webkit'];
      return await browserFactory.createConfiguredBrowserAutomation({
        preferredTypes,
        autoSelect: true
      });
    } catch (error) {
      // 如果自动选择失败，回退到默认的 Puppeteer
      console.log(chalk.yellow(`⚠️  自动选择浏览器失败，使用默认 Puppeteer: ${error.message}`));
      return browserFactory.createBrowserAutomation('puppeteer');
    }
  }

  /**
   * 验证单个页面
   */
  async validateSinglePage(route) {
    const url = this.buildPageUrl(route);
    const result = {
      route: route,
      url: url,
      success: false,
      errors: [],
      warnings: [],
      consoleMessages: [],
      networkErrors: [],
      navigationHistory: [],
      loadTime: 0,
      timestamp: new Date().toISOString(),
      needsLogin: false,
      loginAttempted: false,
      loginConsoleErrors: []
    };

    try {
      const page = await this.browser.newPage();
      const startTime = Date.now();

      // 设置页面监听器
      this.setupPageListeners(page, result);

      // 设置超时
      page.setDefaultTimeout(this.options.pageTimeout);
      page.setDefaultNavigationTimeout(this.options.navigationTimeout);

      // 导航到页面
      const navigationResult = await this.navigateToPage(page, url, result);
      if (!navigationResult.success) {
        result.errors.push(...navigationResult.errors);
        result.loadTime = Date.now() - startTime;
        await page.close();
        return result;
      }

      // 等待页面准备就绪
      await this.waitForPageReady(page);

      // 截图
      try {
        const screenshotPath = await this.screenshotManager.takeScreenshot(page, route);
        result.screenshotPath = screenshotPath;
      } catch (screenshotError) {
        result.warnings.push(`截图失败: ${screenshotError.message}`);
      }

      // 记录加载时间
      result.loadTime = Date.now() - startTime;

      // 输出导航历史
      this.navigationTracker.logNavigationHistory(result);

      // 如果没有错误，标记为成功
      if (result.errors.length === 0) {
        result.success = true;
      }

      await page.close();
      return result;

    } catch (error) {
      result.errors.push({
        type: 'validation-error',
        message: `页面验证异常: ${error.message}`,
        stack: error.stack
      });
      result.loadTime = Date.now() - (result.startTime || Date.now());
      return result;
    }
  }

  /**
   * 设置页面监听器
   */
  setupPageListeners(page, result) {
    // 设置导航跟踪
    this.navigationTracker.setupNavigationTracking(page, result);

    // 控制台消息监听
    page.on('console', (msg) => {
      const message = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      };

      result.consoleMessages.push(message);

      // 捕获错误级别的控制台消息
      if (msg.type() === 'error') {
        result.errors.push({
          type: 'console-error',
          message: msg.text(),
          timestamp: new Date().toISOString()
        });
      }
    });

    // 页面错误监听
    page.on('pageerror', (error) => {
      result.errors.push({
        type: 'page-error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
    });

    // 网络错误监听
    page.on('requestfailed', (request) => {
      result.networkErrors.push({
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * 导航到页面
   */
  async navigateToPage(page, url, result) {
    try {
      // 访问页面
      const response = await page.goto(url, {
        waitUntil: ['domcontentloaded', 'networkidle0'],
        timeout: this.options.navigationTimeout
      });

      // 检查响应状态
      if (response && !response.ok()) {
        result.warnings.push(`HTTP ${response.status()}: ${response.statusText()}`);
      }

      // 等待页面初始化
      await this.sleep(3000);

      // 记录导航后的跳转
      const finalUrl = page.url();
      this.navigationTracker.recordPostNavigationRedirect(url, finalUrl, result);

      // 检查是否需要登录
      const needsLogin = await this.checkIfNeedsLogin(page);
      result.needsLogin = needsLogin;

      if (needsLogin && !this.options.skipLogin) {
        console.log(chalk.yellow(`    🔐 检测到需要登录，尝试自动登录...`));
        result.loginAttempted = true;

        const loginResult = await this.autoLoginManager.attemptLogin(page);
        if (loginResult.success) {
          console.log(chalk.green(`    ✅ 自动登录成功`));
          await this.sleep(2000);
        } else {
          console.log(chalk.red(`    ❌ 自动登录失败: ${loginResult.error}`));
          result.warnings.push(`自动登录失败: ${loginResult.error}`);
        }
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        errors: [{
          type: 'navigation-error',
          message: `页面导航失败: ${error.message}`,
          stack: error.stack,
          timestamp: new Date().toISOString()
        }]
      };
    }
  }

  /**
   * 构建页面URL
   */
  buildPageUrl(route) {
    const baseUrl = this.devServerManager.getBaseUrl();
    if (this.options.routerMode === 'hash') {
      return `${baseUrl}/#${route.path}`;
    } else {
      return `${baseUrl}${route.path}`;
    }
  }

  /**
   * 检查页面是否需要登录
   */
  async checkIfNeedsLogin(page) {
    try {
      const currentUrl = page.url();
      const loginPaths = ['/login', '/signin', '/auth', '/authentication'];
      const isOnLoginPage = loginPaths.some(path => currentUrl.includes(path));

      if (isOnLoginPage) {
        return true;
      }

      // 检查页面是否有登录表单
      const hasLoginForm = await page.evaluate(() => {
        const loginSelectors = [
          '.login-form',
          '.login-container',
          'form[name="login"]',
          'form[id="login"]',
          'input[name="username"]',
          'input[name="email"]'
        ];

        return loginSelectors.some(selector => {
          try {
            return document.querySelector(selector) !== null;
          } catch (e) {
            return false;
          }
        });
      });

      return hasLoginForm;

    } catch (error) {
      return false;
    }
  }

  /**
   * 等待页面准备就绪
   */
  async waitForPageReady(page) {
    const maxWaitTime = 5000;
    const checkInterval = 200;
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      try {
        const isReady = await page.evaluate(() => {
          if (document.readyState !== 'complete') {
            return false;
          }

          const hasVue = !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));
          const loadingElements = document.querySelectorAll([
            '.loading',
            '.spinner',
            '.loader'
          ].join(','));

          const hasVisibleLoading = Array.from(loadingElements).some(el => {
            const style = window.getComputedStyle(el);
            return style.display !== 'none' && style.visibility !== 'hidden';
          });

          return hasVue && !hasVisibleLoading;
        });

        if (isReady) {
          return;
        }

        await this.sleep(checkInterval);
        waitTime += checkInterval;

      } catch (error) {
        break;
      }
    }

    // 如果超时，使用固定等待时间
    if (waitTime >= maxWaitTime) {
      await this.sleep(2000);
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }

      await this.devServerManager.stopDevServer();

      console.log(chalk.green('✅ 资源清理完成'));
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  清理资源时出错: ${error.message}`));
      }
    }
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = PageValidationOrchestrator;
